import 'dart:async';
import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';
import '../constants/app_theme.dart';
import '../services/localization_service.dart';
import '../services/api_service.dart';
import 'page_not_found_screen.dart';

class WebViewScreen extends StatefulWidget {
  final String title;
  final String url;

  const WebViewScreen({super.key, required this.title, required this.url});

  @override
  State<WebViewScreen> createState() => _WebViewScreenState();
}

class _WebViewScreenState extends State<WebViewScreen> {
  WebViewController? _controller;
  bool _isLoading = true;
  bool _isLocalFile = false;
  bool _isRefreshing = false;
  bool _hasError = false;
  String? _errorMessage;
  DateTime? _lastRefreshTime;
  Timer? _loadingTimer;

  @override
  void initState() {
    super.initState();
    _initializeWebView();
  }

  Future<void> _initializeWebView() async {
    // 取消之前的超时定时器
    _loadingTimer?.cancel();

    // 重置错误状态
    if (mounted) {
      setState(() {
        _hasError = false;
        _errorMessage = null;
        _isLoading = true;
      });
    }

    // 设置加载超时定时器（30秒）
    _loadingTimer = Timer(const Duration(seconds: 30), () {
      if (mounted && _isLoading && !_hasError) {
        debugPrint('WebViewScreen: 加载超时，显示404页面');
        setState(() {
          _hasError = true;
          _errorMessage = 'Loading timeout';
          _isLoading = false;
        });
      }
    });

    _controller =
        WebViewController()
          ..setJavaScriptMode(JavaScriptMode.unrestricted)
          ..setNavigationDelegate(
            NavigationDelegate(
              onProgress: (int progress) {
                // Update loading progress
              },
              onPageStarted: (String url) {
                if (mounted) {
                  setState(() {
                    _isLoading = true;
                    _hasError = false;
                  });
                }
              },
              onPageFinished: (String url) {
                // 取消超时定时器
                _loadingTimer?.cancel();
                if (mounted) {
                  setState(() {
                    _isLoading = false;
                  });
                }
              },
              onWebResourceError: (WebResourceError error) {
                debugPrint('WebViewScreen: WebResource错误 - ${error.description}, 错误类型: ${error.errorType}, 是否本地文件: $_isLocalFile');

                // 只有在加载主页面失败时才显示404页面
                // 对于本地文件，不应该因为内部资源失败而显示404
                if (mounted && !_isLocalFile) {
                  // 只处理主要的页面加载错误，忽略资源加载错误
                  if (error.errorType == WebResourceErrorType.hostLookup ||
                      error.errorType == WebResourceErrorType.connect ||
                      error.errorType == WebResourceErrorType.timeout ||
                      error.errorType == WebResourceErrorType.fileNotFound ||
                      error.errorType == WebResourceErrorType.unknown ||
                      error.errorType == WebResourceErrorType.badUrl) {
                    debugPrint('WebViewScreen: 显示404页面 - ${error.description}');
                    // 取消超时定时器
                    _loadingTimer?.cancel();
                    setState(() {
                      _hasError = true;
                      _errorMessage = error.description;
                      _isLoading = false;
                    });
                  } else {
                    debugPrint('WebViewScreen: 忽略资源错误 - ${error.description}');
                  }
                } else if (_isLocalFile) {
                  debugPrint('WebViewScreen: 本地文件资源错误，忽略 - ${error.description}');
                }
              },
            ),
          );

    // Check if it is a local HTML file
    _isLocalFile = widget.url.startsWith('assets/');
    if (_isLocalFile) {
      await _loadLocalHtml();
    } else {
      await _loadRemoteUrl();
    }
  }

  Future<void> _loadRemoteUrl() async {
    try {
      final processedUrl = await _processUrl(widget.url);

      // 验证URL格式
      final uri = Uri.tryParse(processedUrl);
      if (uri == null || !uri.hasScheme || !uri.hasAuthority) {
        throw FormatException('无效的URL格式: $processedUrl');
      }

      _controller?.loadRequest(uri);
      debugPrint('WebViewScreen: 开始加载URL - $processedUrl');

    } catch (e) {
      debugPrint('WebViewScreen: URL处理失败 - ${widget.url}, 错误: $e');
      if (mounted) {
        setState(() {
          _hasError = true;
          _errorMessage = 'URL格式错误: ${widget.url}\n错误详情: $e';
          _isLoading = false;
        });
      }
    }
  }

  /// 处理各种URL格式，返回完整的URL
  Future<String> _processUrl(String originalUrl) async {
    String url = originalUrl.trim();

    // 1. 如果已经是完整的URL（包含协议），直接使用
    if (url.startsWith('http://') || url.startsWith('https://')) {
      debugPrint('WebViewScreen: 完整URL直接使用 - $url');
      return url;
    }

    // 2. 如果是相对路径（以/开头），使用服务器配置地址
    if (url.startsWith('/')) {
      final baseUrl = await ApiService.getBaseUrl();
      // 移除API路径，只保留服务器地址
      final serverUrl = baseUrl.replaceAll('/api', '');
      url = '$serverUrl$url';
      debugPrint('WebViewScreen: 相对路径转换 - 原始: $originalUrl, 转换后: $url');
      return url;
    }

    // 3. 如果包含域名特征（包含点号），添加https协议
    if (url.contains('.')) {
      // 检查是否是IP地址格式
      final ipRegex = RegExp(r'^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}');
      if (ipRegex.hasMatch(url)) {
        // IP地址，使用http协议（通常内网IP不支持https）
        url = 'http://$url';
        debugPrint('WebViewScreen: IP地址添加http协议 - 原始: $originalUrl, 转换后: $url');
      } else {
        // 域名，使用https协议
        url = 'https://$url';
        debugPrint('WebViewScreen: 域名添加https协议 - 原始: $originalUrl, 转换后: $url');
      }
      return url;
    }

    // 4. 如果包含端口号（格式如：localhost:8080）
    if (url.contains(':') && !url.contains('://')) {
      url = 'http://$url';
      debugPrint('WebViewScreen: 地址:端口格式添加http协议 - 原始: $originalUrl, 转换后: $url');
      return url;
    }

    // 5. 其他情况，可能是不完整的域名，尝试添加https
    url = 'https://$url';
    debugPrint('WebViewScreen: 默认添加https协议 - 原始: $originalUrl, 转换后: $url');
    return url;
  }

  Future<void> _loadLocalHtml() async {
    try {
      // Extract the base file path without query parameters
      final uri = Uri.parse(widget.url);
      final basePath = uri.path;

      debugPrint('WebViewScreen: 尝试加载本地文件 - $basePath');

      final String htmlContent = await DefaultAssetBundle.of(
        context,
      ).loadString(basePath);

      debugPrint('WebViewScreen: 本地文件加载成功，长度: ${htmlContent.length}');

      // Inject query parameters into the HTML if needed
      String modifiedHtml = htmlContent;
      if (uri.hasQuery) {
        final queryParams = uri.queryParameters;
        debugPrint('WebViewScreen: 注入查询参数 - $queryParams');

        // Add JavaScript to handle query parameters
        final scriptTag = '''
<script>
  window.urlParams = ${_mapToJavaScript(queryParams)};
  // Trigger page initialization with parameters
  document.addEventListener('DOMContentLoaded', function() {
    if (typeof initializeWithParams === 'function') {
      initializeWithParams(window.urlParams);
    }
  });
</script>
''';
        // Insert the script before the closing head tag
        modifiedHtml = htmlContent.replaceFirst('</head>', '$scriptTag</head>');
      }

      await _controller?.loadHtmlString(modifiedHtml);
      debugPrint('WebViewScreen: HTML字符串加载完成');

    } catch (e) {
      debugPrint('WebViewScreen: 加载本地文件失败 - $e');

      if (mounted) {
        setState(() {
          _hasError = true;
          _errorMessage = e.toString();
          _isLoading = false;
        });
      }
    }
  }

  String _mapToJavaScript(Map<String, String> map) {
    final entries = map.entries
        .map((e) => '"${e.key}": "${e.value}"')
        .join(', ');
    return '{$entries}';
  }

  void _refreshWebView() {
    // 检查是否在刷新中或距离上次刷新不足3秒
    final now = DateTime.now();
    if (_isRefreshing ||
        (_lastRefreshTime != null &&
            now.difference(_lastRefreshTime!).inSeconds < 3)) {
      return;
    }

    setState(() {
      _isRefreshing = true;
      _isLoading = true;
    });

    _lastRefreshTime = now;

    if (_isLocalFile) {
      // For local files, reload HTML content
      _loadLocalHtml();
    } else {
      // For network URLs, use standard reload method
      _controller?.reload();
    }

    // Show refresh hint
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(LocalizationService.t('refreshing_page')),
        duration: const Duration(seconds: 1),
        backgroundColor: AppTheme.primaryColor,
      ),
    );

    // 3秒后重新启用刷新按钮
    Future.delayed(const Duration(seconds: 3), () {
      if (mounted) {
        setState(() {
          _isRefreshing = false;
        });
      }
    });
  }

  @override
  void dispose() {
    _loadingTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // 如果有错误，显示404页面
    if (_hasError) {
      return PageNotFoundScreen(
        title: widget.title,
        description: LocalizationService.t('webview_load_failed'),
        errorMessage: _errorMessage,
        onRetry: () {
          _initializeWebView();
        },
        onGoBack: () => Navigator.of(context).pop(),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(widget.title),
        leading: IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: const Icon(Icons.arrow_back),
        ),
        actions: [
          IconButton(
            onPressed: _isRefreshing ? null : _refreshWebView,
            icon: _isRefreshing
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
                    ),
                  )
                : const Icon(Icons.refresh),
            style: IconButton.styleFrom(
              disabledForegroundColor: Theme.of(context).brightness == Brightness.dark
                  ? AppTheme.darkTextSecondary
                  : AppTheme.textSecondary,
            ),
          ),
        ],
      ),
      body: Stack(
          children: [
            if (_controller != null) WebViewWidget(controller: _controller!),
            if (_isLoading)
              Container(
                color: AppTheme.backgroundColor,
                child: const Center(
                  child: CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(
                      AppTheme.primaryColor,
                    ),
                  ),
                ),
              ),
          ],
        ),
    );
  }
}
