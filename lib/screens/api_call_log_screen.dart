import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../constants/app_theme.dart';
import '../services/localization_service.dart';
import '../services/debug_log_service.dart';

class ApiCallLogScreen extends StatefulWidget {
  const ApiCallLogScreen({super.key});

  @override
  State<ApiCallLogScreen> createState() => _ApiCallLogScreenState();
}

class _ApiCallLogScreenState extends State<ApiCallLogScreen> {
  final DebugLogService _debugLogService = DebugLogService();
  List<LogEntry> _logs = [];
  bool _isLoading = true;
  String? _selectedFilter;

  @override
  void initState() {
    super.initState();
    _loadLogs();
  }

  Future<void> _loadLogs() async {
    setState(() {
      _isLoading = true;
    });

    try {
      await _debugLogService.initialize();
      // 只获取API相关的日志
      final allLogs = _debugLogService.logs;
      _logs = allLogs.where((log) => 
        log.tag == 'API_REQUEST' || 
        log.tag == 'API_RESPONSE' ||
        log.tag == 'API' ||
        log.tag == 'LOGIN' ||
        log.message.contains('API') ||
        log.message.contains('请求') ||
        log.message.contains('响应')
      ).toList();
    } catch (e) {
      debugPrint('加载API日志失败: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _clearLogs() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(LocalizationService.t('clear_api_logs')),
        content: Text(LocalizationService.t('confirm_clear_logs')),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(LocalizationService.t('cancel')),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: Text(LocalizationService.t('confirm')),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      await _debugLogService.clearLogs();
      await _loadLogs();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(LocalizationService.t('api_logs_cleared')),
            backgroundColor: AppTheme.successColor,
          ),
        );
      }
    }
  }

  Future<void> _copyLog(LogEntry log) async {
    await Clipboard.setData(ClipboardData(text: log.formattedMessage));
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(LocalizationService.t('api_log_copied')),
          backgroundColor: AppTheme.successColor,
        ),
      );
    }
  }

  Future<void> _copyAllLogs() async {
    final filteredLogs = _getFilteredLogs();
    final logsText = filteredLogs.map((log) => log.formattedMessage).join('\n\n');
    await Clipboard.setData(ClipboardData(text: logsText));
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(LocalizationService.t('api_log_copied')),
          backgroundColor: AppTheme.successColor,
        ),
      );
    }
  }

  void _showLogDetails(LogEntry log) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(LocalizationService.t('view_api_log')),
        content: SingleChildScrollView(
          child: SelectableText(
            log.formattedMessage,
            style: const TextStyle(fontFamily: 'monospace'),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => _copyLog(log),
            child: Text(LocalizationService.t('copy')),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(LocalizationService.t('close')),
          ),
        ],
      ),
    );
  }

  List<LogEntry> _getFilteredLogs() {
    if (_selectedFilter == null) {
      return _logs;
    }
    return _logs.where((log) => log.tag == _selectedFilter).toList();
  }

  Map<String, int> _getLogStats() {
    final stats = <String, int>{};
    for (final log in _logs) {
      final tag = log.tag ?? 'OTHER';
      stats[tag] = (stats[tag] ?? 0) + 1;
    }
    return stats;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.getBackgroundColor(context),
      appBar: AppBar(
        title: Text(LocalizationService.t('api_call_log_title')),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadLogs,
            tooltip: LocalizationService.t('refresh'),
          ),
          IconButton(
            icon: const Icon(Icons.copy),
            onPressed: _copyAllLogs,
            tooltip: LocalizationService.t('copy_all'),
          ),
          IconButton(
            icon: const Icon(Icons.clear_all),
            onPressed: _clearLogs,
            tooltip: LocalizationService.t('clear_api_logs'),
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _logs.isEmpty
              ? _buildEmptyState()
              : Column(
                  children: [
                    _buildStatsCard(),
                    if (_selectedFilter != null) _buildFilterHeader(),
                    Expanded(child: _buildLogsList()),
                  ],
                ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.api_outlined,
            size: 64,
            color: AppTheme.textSecondary,
          ),
          const SizedBox(height: 16),
          Text(
            LocalizationService.t('no_api_logs'),
            style: TextStyle(
              fontSize: 18,
              color: AppTheme.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatsCard() {
    final stats = _getLogStats();
    if (stats.isEmpty) return const SizedBox.shrink();

    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              LocalizationService.t('log_statistics'),
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: stats.entries.map((entry) {
                final isSelected = _selectedFilter == entry.key;
                return GestureDetector(
                  onTap: () {
                    setState(() {
                      _selectedFilter = isSelected ? null : entry.key;
                    });
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: isSelected ? AppTheme.primaryColor : AppTheme.getCardColor(context),
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: isSelected ? AppTheme.primaryColor : AppTheme.getBorderColor(context),
                      ),
                    ),
                    child: Text(
                      '${entry.key} (${entry.value})',
                      style: TextStyle(
                        color: isSelected ? Colors.white : AppTheme.textPrimary,
                        fontSize: 12,
                      ),
                    ),
                  ),
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFilterHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      color: AppTheme.primaryColor.withValues(alpha: 0.1),
      child: Row(
        children: [
          Text(
            '${LocalizationService.t('filter_by')}: $_selectedFilter',
            style: TextStyle(
              color: AppTheme.primaryColor,
              fontWeight: FontWeight.bold,
            ),
          ),
          const Spacer(),
          TextButton(
            onPressed: () {
              setState(() {
                _selectedFilter = null;
              });
            },
            child: Text(LocalizationService.t('show_all')),
          ),
        ],
      ),
    );
  }

  Widget _buildLogsList() {
    final filteredLogs = _getFilteredLogs();
    
    if (filteredLogs.isEmpty) {
      return Center(
        child: Text(
          '${LocalizationService.t('no_logs_found')} $_selectedFilter',
          style: TextStyle(
            color: AppTheme.textSecondary,
            fontSize: 16,
          ),
        ),
      );
    }

    return ListView.builder(
      itemCount: filteredLogs.length,
      itemBuilder: (context, index) {
        final log = filteredLogs[index];
        return _buildLogItem(log);
      },
    );
  }

  Widget _buildLogItem(LogEntry log) {
    final isRequest = log.tag == 'API_REQUEST';
    final isResponse = log.tag == 'API_RESPONSE';
    
    Color cardColor = AppTheme.getCardColor(context);
    Color borderColor = AppTheme.getBorderColor(context);
    IconData icon = Icons.info_outline;
    
    if (isRequest) {
      cardColor = Colors.blue.withValues(alpha: 0.1);
      borderColor = Colors.blue;
      icon = Icons.arrow_upward;
    } else if (isResponse) {
      cardColor = Colors.green.withValues(alpha: 0.1);
      borderColor = Colors.green;
      icon = Icons.arrow_downward;
    }

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      color: cardColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
        side: BorderSide(color: borderColor, width: 1),
      ),
      child: ListTile(
        leading: Icon(icon, color: borderColor),
        title: Text(
          log.tag ?? 'API',
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              log.message.length > 100 
                  ? '${log.message.substring(0, 100)}...'
                  : log.message,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 4),
            Text(
              '${log.timestamp.hour.toString().padLeft(2, '0')}:'
              '${log.timestamp.minute.toString().padLeft(2, '0')}:'
              '${log.timestamp.second.toString().padLeft(2, '0')}',
              style: TextStyle(
                fontSize: 12,
                color: AppTheme.textSecondary,
              ),
            ),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: const Icon(Icons.copy, size: 20),
              onPressed: () => _copyLog(log),
              tooltip: LocalizationService.t('copy_api_log'),
            ),
            IconButton(
              icon: const Icon(Icons.visibility, size: 20),
              onPressed: () => _showLogDetails(log),
              tooltip: LocalizationService.t('view_api_log'),
            ),
          ],
        ),
        onTap: () => _showLogDetails(log),
      ),
    );
  }
}
